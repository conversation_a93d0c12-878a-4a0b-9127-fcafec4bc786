<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COSMIC功能拆解系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .content-area {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 12px;
        }
        .file-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }
        .file-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .progress {
            height: 4px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4 class="text-white">COSMIC系统</h4>
                    <small class="text-white-50">功能拆解与文档生成</small>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#" data-section="upload">
                        <i class="bi bi-cloud-upload me-2"></i>功能清单
                    </a>
                    <a class="nav-link" href="#" data-section="decompose">
                        <i class="bi bi-diagram-3 me-2"></i>功能拆解
                    </a>
                    <a class="nav-link" href="#" data-section="validate">
                        <i class="bi bi-check-circle me-2"></i>检查修复
                    </a>
                    <a class="nav-link" href="#" data-section="document">
                        <i class="bi bi-file-text me-2"></i>文档生成
                    </a>
                    <a class="nav-link" href="#" data-section="prompts">
                        <i class="bi bi-pencil-square me-2"></i>提示词管理
                    </a>
                    <a class="nav-link" href="#" data-section="config">
                        <i class="bi bi-gear me-2"></i>系统配置
                    </a>
                </nav>
            </div>
            
            <!-- 主内容区域 -->
            <div class="col-md-9 col-lg-10 content-area p-4">
                <!-- 文件上传区域 -->
                <div id="upload-section" class="section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-cloud-upload me-2"></i>功能清单文件管理</h2>
                        <button class="btn btn-primary" onclick="refreshFiles()">
                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                        </button>
                    </div>
                    
                    <!-- 上传区域 -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="upload-area" id="uploadArea">
                                <i class="bi bi-cloud-upload display-4 text-muted mb-3"></i>
                                <h5>拖拽文件到此处或点击选择</h5>
                                <p class="text-muted">支持 .xlsx, .xls 格式，最大 16MB</p>
                                <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
                                <button class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                                    选择文件
                                </button>
                            </div>
                            <div id="uploadProgress" class="mt-3" style="display: none;">
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 已上传文件列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">已上传文件 ({{ uploaded_files|length }})</h5>
                        </div>
                        <div class="card-body">
                            <div id="uploadedFilesList">
                                {% for file in uploaded_files %}
                                <div class="file-item" data-filename="{{ file.name }}">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-1">{{ file.name }}</h6>
                                            <small class="text-muted">
                                                {{ "%.2f"|format(file.size/1024/1024) }} MB | {{ file.modified_str }}
                                            </small>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="badge bg-success status-badge">已上传</span>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="previewFile('uploads', '{{ file.name }}')">
                                                    <i class="bi bi-eye"></i> 预览
                                                </button>
                                                <button class="btn btn-outline-success" onclick="downloadFile('uploads', '{{ file.name }}')">
                                                    <i class="bi bi-download"></i> 下载
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteFile('uploads', '{{ file.name }}')">
                                                    <i class="bi bi-trash"></i> 删除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                                
                                {% if not uploaded_files %}
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-inbox display-4"></i>
                                    <p class="mt-2">暂无上传文件</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 功能拆解区域 -->
                <div id="decompose-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-diagram-3 me-2"></i>COSMIC功能拆解</h2>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">选择文件和提示词</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">选择功能清单文件</label>
                                    <select class="form-select" id="decomposeFileSelect">
                                        <option value="">请选择文件...</option>
                                        {% for file in uploaded_files %}
                                        <option value="{{ file.name }}">{{ file.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">选择提示词文件</label>
                                    <select class="form-select" id="promptFileSelect">
                                        {% for prompt in prompt_files %}
                                        <option value="{{ prompt.path }}">{{ prompt.display_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-primary" onclick="startDecompose()">
                                    <i class="bi bi-play-circle me-1"></i>开始拆解
                                </button>
                                <div id="decomposeTimer" class="mt-2 text-info" style="display: none;">
                                    <i class="bi bi-clock me-1"></i>已用时: 00:00
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 拆解结果文件列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">拆解结果文件 ({{ output_files|length }})</h5>
                        </div>
                        <div class="card-body">
                            <div id="outputFilesList">
                                {% for file in output_files %}
                                <div class="file-item" data-filename="{{ file.name }}">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-1">{{ file.name }}</h6>
                                            <small class="text-muted">
                                                {{ "%.2f"|format(file.size/1024/1024) }} MB | {{ file.modified_str }}
                                            </small>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="badge bg-info status-badge">拆解完成</span>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="previewFile('outputs', '{{ file.name }}')">
                                                    <i class="bi bi-eye"></i> 预览
                                                </button>
                                                <button class="btn btn-outline-success" onclick="downloadFile('outputs', '{{ file.name }}')">
                                                    <i class="bi bi-download"></i> 下载
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteFile('outputs', '{{ file.name }}')">
                                                    <i class="bi bi-trash"></i> 删除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                                
                                {% if not output_files %}
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-inbox display-4"></i>
                                    <p class="mt-2">暂无拆解结果文件</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 检查修复区域 -->
                <div id="validate-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-check-circle me-2"></i>检查修复</h2>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">选择要检查的拆解文件</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <label class="form-label">选择拆解结果文件</label>
                                    <select class="form-select" id="validateFileSelect">
                                        <option value="">请选择文件...</option>
                                        {% for file in output_files %}
                                        <option value="{{ file.name }}">{{ file.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button class="btn btn-warning w-100" onclick="startValidateRepair()">
                                        <i class="bi bi-tools me-1"></i>开始检查修复
                                    </button>
                                </div>
                            </div>
                            <div id="repairTimer" class="mt-2 text-warning" style="display: none;">
                                <i class="bi bi-clock me-1"></i>已用时: 00:00
                            </div>
                        </div>
                    </div>

                    <!-- 修复结果文件列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">修复结果文件</h5>
                        </div>
                        <div class="card-body">
                            <div id="repairFilesList">
                                {% for file in output_files %}
                                {% if '_修复_' in file.name %}
                                <div class="file-item" data-filename="{{ file.name }}">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-1">{{ file.name }}</h6>
                                            <small class="text-muted">
                                                {{ "%.2f"|format(file.size/1024/1024) }} MB | {{ file.modified_str }}
                                            </small>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="badge bg-warning status-badge">已修复</span>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="previewFile('outputs', '{{ file.name }}')">
                                                    <i class="bi bi-eye"></i> 预览
                                                </button>
                                                <button class="btn btn-outline-success" onclick="downloadFile('outputs', '{{ file.name }}')">
                                                    <i class="bi bi-download"></i> 下载
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteFile('outputs', '{{ file.name }}')">
                                                    <i class="bi bi-trash"></i> 删除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                {% endfor %}

                                <div class="text-center text-muted py-4" id="noRepairFiles" {% if has_repair_files %}style="display: none;"{% endif %}>
                                    <i class="bi bi-inbox display-4"></i>
                                    <p class="mt-2">暂无修复结果文件</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文档生成区域 -->
                <div id="document-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-file-text me-2"></i>文档生成</h2>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">生成Markdown文档</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <label class="form-label">选择拆解结果文件</label>
                                    <select class="form-select" id="documentFileSelect">
                                        <option value="">请选择文件...</option>
                                        {% for file in output_files %}
                                        <option value="{{ file.name }}">{{ file.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button class="btn btn-success w-100" onclick="startGenerateDocument()">
                                        <i class="bi bi-file-earmark-text me-1"></i>生成文档
                                    </button>
                                </div>
                            </div>
                            <div id="documentTimer" class="mt-2 text-success" style="display: none;">
                                <i class="bi bi-clock me-1"></i>已用时: 00:00
                            </div>
                        </div>
                    </div>

                    <!-- Markdown文档列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">生成的文档 ({{ word_files|length }})</h5>
                        </div>
                        <div class="card-body">
                            <div id="wordFilesList">
                                {% for file in word_files %}
                                <div class="file-item" data-filename="{{ file.name }}">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-1">{{ file.name }}</h6>
                                            <small class="text-muted">
                                                {{ "%.2f"|format(file.size/1024/1024) }} MB | {{ file.modified_str }}
                                            </small>
                                        </div>
                                        <div class="col-md-3">
                                            {% if file.name.endswith('.md') %}
                                            <span class="badge bg-primary status-badge">Markdown</span>
                                            {% else %}
                                            <span class="badge bg-info status-badge">Word</span>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="previewFile('words', '{{ file.name }}')">
                                                    <i class="bi bi-eye"></i> 预览
                                                </button>
                                                {% if file.name.endswith('.md') %}
                                                <button class="btn btn-outline-info" onclick="convertToWord('{{ file.name }}')">
                                                    <i class="bi bi-file-word"></i> 转Word
                                                </button>
                                                {% endif %}
                                                <button class="btn btn-outline-success" onclick="downloadFile('words', '{{ file.name }}')">
                                                    <i class="bi bi-download"></i> 下载
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteFile('words', '{{ file.name }}')">
                                                    <i class="bi bi-trash"></i> 删除
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}

                                {% if not word_files %}
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-inbox display-4"></i>
                                    <p class="mt-2">暂无生成的文档</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 提示词管理区域 -->
                <div id="prompts-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-pencil-square me-2"></i>COSMIC拆解提示词管理</h2>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">编辑COSMIC拆解提示词</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">选择提示词文件</label>
                                    <select class="form-select" id="promptEditSelect" onchange="loadPromptContent()">
                                        <option value="">请选择提示词文件...</option>
                                        {% for prompt in prompt_files %}
                                        <option value="{{ prompt.path }}" data-is-system="{{ prompt.is_system|default(false)|lower }}">{{ prompt.display_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <button class="btn btn-primary me-2" onclick="savePromptContent()">
                                        <i class="bi bi-save me-1"></i>保存
                                    </button>
                                    <button class="btn btn-success me-2" id="saveAsBtn" onclick="savePromptAs()">
                                        <i class="bi bi-save-fill me-1"></i>另存为
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="resetPromptContent()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>重置
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label">提示词内容</label>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <input type="radio" class="btn-check" name="promptViewMode" id="editMode" autocomplete="off" checked>
                                        <label class="btn btn-outline-primary" for="editMode">编辑</label>

                                        <input type="radio" class="btn-check" name="promptViewMode" id="previewMode" autocomplete="off">
                                        <label class="btn btn-outline-primary" for="previewMode">预览</label>
                                    </div>
                                </div>

                                <div id="promptEditArea">
                                    <textarea class="form-control" id="promptContent" rows="20" placeholder="请选择提示词文件后编辑内容..."></textarea>
                                </div>

                                <div id="promptPreviewArea" style="display: none;">
                                    <div class="border rounded p-3" style="min-height: 500px; background-color: #f8f9fa;">
                                        <div id="promptPreviewContent">
                                            <p class="text-muted">请先编辑内容，然后切换到预览模式查看效果</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>提示：</strong>此功能仅用于编辑COSMIC功能拆解相关的提示词文件。修改后的提示词将在下次拆解时生效。
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 系统配置区域 -->
                <div id="config-section" class="section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-gear me-2"></i>系统配置</h2>
                        <div>
                            <button class="btn btn-outline-primary me-2" onclick="loadConfig()">
                                <i class="bi bi-arrow-clockwise me-1"></i>刷新配置
                            </button>
                            <button class="btn btn-success" onclick="saveConfig()">
                                <i class="bi bi-save me-1"></i>保存配置
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 基本配置 -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">基本配置</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">批次大小</label>
                                        <input type="number" class="form-control" id="config_BATCH_COUNT" placeholder="30">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">数据目录</label>
                                        <input type="text" class="form-control" id="config_DATA_DIR" placeholder="data/">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">线程数量</label>
                                        <input type="number" class="form-control" id="config_THREAD_COUNT" placeholder="4">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">最大线程数</label>
                                        <input type="number" class="form-control" id="config_MAX_THREAD_COUNT" placeholder="4">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- LLM配置 -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">大模型配置</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Provider类型</label>
                                        <select class="form-select" id="config_LLM_PROVIDER" onchange="updateProviderConfig()">
                                            <option value="openai">OpenAI兼容</option>
                                            <option value="dashscope">阿里云百炼</option>
                                            <option value="siliconflow">硅基流动</option>
                                        </select>
                                    </div>

                                    <!-- OpenAI兼容配置 -->
                                    <div id="openai-config" class="provider-config">
                                        <div class="mb-3">
                                            <label class="form-label">API端点</label>
                                            <input type="text" class="form-control" id="config_OPENAI_API_BASE" placeholder="https://api.openai.com/v1">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">模型名称</label>
                                            <input type="text" class="form-control" id="config_OPENAI_MODEL" placeholder="gpt-4">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">API密钥</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="config_OPENAI_API_KEY" placeholder="sk-...">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('config_OPENAI_API_KEY')">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 阿里云百炼配置 -->
                                    <div id="dashscope-config" class="provider-config" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">模型名称</label>
                                            <select class="form-select" id="config_DASHSCOPE_MODEL">
                                                <option value="qwen-turbo">qwen-turbo</option>
                                                <option value="qwen-plus">qwen-plus</option>
                                                <option value="qwen-max">qwen-max</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">API密钥</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="config_DASHSCOPE_API_KEY" placeholder="sk-...">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('config_DASHSCOPE_API_KEY')">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 硅基流动配置 -->
                                    <div id="siliconflow-config" class="provider-config" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">模型名称</label>
                                            <select class="form-select" id="config_SILICONFLOW_MODEL">
                                                <option value="Qwen/Qwen2-7B-Instruct">Qwen2-7B-Instruct</option>
                                                <option value="Qwen/Qwen2-72B-Instruct">Qwen2-72B-Instruct</option>
                                                <option value="deepseek-ai/DeepSeek-V2-Chat">DeepSeek-V2-Chat</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">API密钥</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="config_SILICONFLOW_API_KEY" placeholder="sk-...">
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('config_SILICONFLOW_API_KEY')">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">QPM限制</label>
                                                <input type="number" class="form-control" id="config_API_QPM" placeholder="600">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">TPM限制</label>
                                                <input type="number" class="form-control" id="config_API_TPM" placeholder="10000000">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 嵌入模型配置 -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">嵌入模型配置</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">嵌入模型</label>
                                        <input type="text" class="form-control" id="config_EMBEDDING_MODEL" placeholder="embed">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">嵌入API端点</label>
                                        <input type="text" class="form-control" id="config_EMBEDDING_API_BASE" placeholder="https://ai.secsign.online:38080">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">嵌入API密钥</label>
                                        <input type="password" class="form-control" id="config_EMBEDDING_API_KEY" placeholder="sk-...">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 校验配置 -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">校验配置</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">校验批次大小</label>
                                        <input type="number" class="form-control" id="config_CHECK_BATCH_COUNT" placeholder="100">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">排除字段</label>
                                        <input type="text" class="form-control" id="config_CHECK_EXCLUDED_FIELDS" placeholder="预估工作量（人天）,功能描述">
                                        <small class="form-text text-muted">多个字段用逗号分隔</small>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">输出目录</label>
                                        <input type="text" class="form-control" id="config_CHECK_OUTPUT_DIR" placeholder="debug">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>注意：</strong>修改配置后需要重启系统才能生效。请谨慎修改API密钥等敏感信息。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>
